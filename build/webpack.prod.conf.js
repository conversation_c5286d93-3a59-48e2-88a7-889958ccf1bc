const webpack = require('webpack');
const { merge } = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const BranchPlugin = require('@tntd/webpack-branch-plugin');
const baseWebpackConfig = require('./webpack.base.conf');
const config = require('./config');
const path = require('path');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const dllPath = require('@tntd/dll');

const { staticPath } = config;

module.exports = merge(baseWebpackConfig, {
    mode: 'production',
    output: {
        chunkFilename: staticPath + '/js/[name].[chunkhash:8].js',
        library: config.projectCode,
        libraryTarget: 'umd',
        chunkLoadingGlobal: `webpackJsonp_${config.projectCode}`
    },
    optimization: {
        splitChunks: {
            cacheGroups: {
                styles: {
                    name: 'styles',
                    test: /\.(css|less)(\?.*)?$/,
                    chunks: 'all',
                    enforce: true
                }
            }
        },
        minimize: true,
        minimizer: [
            new TerserPlugin({
                extractComments: false,
                terserOptions: {
                    output: {
                        comments: false // 此配置最重要，无此配置无法删除声明注释
                    }
                }
            }),
            new CssMinimizerPlugin()
        ]
    },
    module: baseWebpackConfig.module(),
    plugins: [
        new webpack.DefinePlugin({
            'process.env': JSON.stringify(config.build.env)
        }),
        new CleanWebpackPlugin({
            cleanOnceBeforeBuildPatterns: [staticPath]
        }),
        new webpack.ContextReplacementPlugin(/moment[\/\\]locale$/, /zh-cn|en-us/),
        new HtmlWebpackPlugin({
            filename: staticPath + '/index.html',
            template: path.resolve(__dirname, '../src/index.ejs'), // 配置html模板的地址
            scriptLoading: 'blocking',
            chunksSortMode: 'none',
            pathPrefix: staticPath
        }),
        new MiniCssExtractPlugin({
            filename: staticPath + '/css/[name].[chunkhash:8].css',
            ignoreOrder: true
        }),
        new CopyPlugin({
            patterns: [
                {
                    from: path.join(__dirname, '../public'),
                    to: config.build.assetsRoot + '/' + staticPath
                },
                { from: dllPath + '/vendor-ie', to: staticPath + '/vendor-ie' }
            ]
        }),
        new BranchPlugin({
            filename: staticPath + '/branch_info.txt'
        })
    ]
});
